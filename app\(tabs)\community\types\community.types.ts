export interface Community {
  id: string;
  name: string;
  description: string;
  members: number;
  image: string;
  joined: boolean;
  isPremium?: boolean;
  isPrivate?: boolean;
  price?: number;
  category?: string;
  lastActive?: string;
}

export interface CommunityItemProps {
  item: Community;
  onJoinToggle: (id: string, isJoining: boolean) => void;
}

export interface LoadingStateProps {
  size?: number;
  fullScreen?: boolean;
  message?: string;
}

export interface UseCommunitiesReturn {
  communities: Community[];
  filteredCommunities: Community[];
  searchQuery: string;
  isLoading: boolean;
  isRefreshing: boolean;
  isLoadingMore: boolean;
  handleSearch: (text: string) => void;
  handleRefresh: () => Promise<void>;
  loadMoreCommunities: () => Promise<void>;
  handleJoinToggle: (id: string, isJoining: boolean) => void;
}

export const BASE_COMMUNITIES: Community[] = [
  {
    id: 'bitcoin-bulls',
    name: 'Bitcoin Bulls',
    description: 'Community for long-term Bitcoin investors and HODLers',
    members: 2547,
    image: 'https://assets.coingecko.com/coins/images/1/large/bitcoin.png',
    joined: true,
  },
  // ... other base communities
];
