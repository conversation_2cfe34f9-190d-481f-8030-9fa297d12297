import { useState, useCallback, useEffect } from 'react';
import { Alert } from 'react-native';

// Types
type CommunityData = {
  posts: any[]; // Replace 'any' with your actual post type
  isLoading: boolean;
  isRefreshing: boolean;
  error: string | null;
  joinCommunity: (communityId: string) => Promise<void>;
  leaveCommunity: (communityId: string) => Promise<void>;
  addPost: (post: any) => Promise<void>; // Replace 'any' with your actual post type
  refreshData: () => Promise<void>;
};

export const useCommunityData = (
  communityId: string,
  initialJoined: boolean = false
): CommunityData => {
  const [posts, setPosts] = useState<any[]>([]); // Replace 'any' with your actual post type
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [isJoined, setIsJoined] = useState<boolean>(initialJoined);

  // Fetch community posts
  const fetchPosts = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // TODO: Replace with actual API call
      // const response = await fetch(`/api/communities/${communityId}/posts`);
      // const data = await response.json();
      // setPosts(data.posts);
      
      // Mock data for now
      const mockPosts = [];
      setPosts(mockPosts);
    } catch (err) {
      console.error('Error fetching community posts:', err);
      setError('Failed to load community posts');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, [communityId]);

  // Join community
  const joinCommunity = useCallback(async (communityId: string) => {
    try {
      setIsLoading(true);
      // TODO: Replace with actual API call
      // await fetch(`/api/communities/${communityId}/join`, { method: 'POST' });
      setIsJoined(true);
      await fetchPosts();
    } catch (err) {
      console.error('Error joining community:', err);
      Alert.alert('Error', 'Failed to join community');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [fetchPosts]);

  // Leave community
  const leaveCommunity = useCallback(async (communityId: string) => {
    try {
      setIsLoading(true);
      // TODO: Replace with actual API call
      // await fetch(`/api/communities/${communityId}/leave`, { method: 'POST' });
      setIsJoined(false);
    } catch (err) {
      console.error('Error leaving community:', err);
      Alert.alert('Error', 'Failed to leave community');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Add post to community
  const addPost = useCallback(async (post: any) => {
    try {
      // TODO: Replace with actual API call
      // const response = await fetch(`/api/communities/${communityId}/posts`, {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(post)
      // });
      // const newPost = await response.json();
      
      // Add to local state
      setPosts(prevPosts => [
        // newPost,
        ...prevPosts
      ]);
    } catch (err) {
      console.error('Error adding post:', err);
      Alert.alert('Error', 'Failed to add post');
      throw err;
    }
  }, [communityId]);

  // Refresh data
  const refreshData = useCallback(async () => {
    setIsRefreshing(true);
    await fetchPosts();
  }, [fetchPosts]);

  // Initial data fetch
  useEffect(() => {
    if (isJoined) {
      fetchPosts();
    }
  }, [fetchPosts, isJoined]);

  return {
    posts,
    isLoading,
    isRefreshing,
    error,
    joinCommunity,
    leaveCommunity,
    addPost,
    refreshData,
  };
};
