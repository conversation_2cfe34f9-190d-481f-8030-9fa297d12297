export interface Theme {
  background: string;
  card: string;
  cardBackground?: string;
  text: string;
  textSecondary?: string;
  textTertiary?: string;
  border: string;
  primary: string;
  notification: string;
  skeletonBackground?: string;
  // Add other theme properties as needed
}

export interface ThemeStyles {
  container: {
    flex: number;
    backgroundColor: string;
  };
  headerContainer: {
    padding: number;
    backgroundColor: string;
  };
  title: {
    fontSize: number;
    fontWeight: string;
    color: string;
    marginBottom: number;
  };
  // Add other style properties as needed
}
