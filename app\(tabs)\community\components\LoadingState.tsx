import React from 'react';
import { View, StyleSheet } from 'react-native';
import LoadingWhale from '../../../../components/ui/LoadingWhale';

interface LoadingStateProps {
  size?: number;
  fullScreen?: boolean;
}

export const LoadingState = ({ size = 24, fullScreen = false }: LoadingStateProps) => (
  <View style={[styles.loaderContainer, fullScreen && styles.fullScreenLoader]}>
    <LoadingWhale size={size} />
  </View>
);

const styles = StyleSheet.create({
  loaderContainer: {
    padding: 20,
    alignItems: 'center',
  },
  fullScreenLoader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
