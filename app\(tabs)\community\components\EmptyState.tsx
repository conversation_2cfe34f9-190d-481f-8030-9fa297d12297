import React, { memo } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Theme } from '../types/theme.types';

interface EmptyStateProps {
  onRefresh: () => void;
  theme: Theme;
  isLoading: boolean;
}

export const EmptyState = memo(({ onRefresh, theme, isLoading }: EmptyStateProps) => (
  <View style={[styles.emptyContainer, { backgroundColor: theme.background }]}>
    <Text style={[styles.emptyText, { color: theme.text }]}>
      {isLoading ? 'Loading communities...' : 'No communities found'}
    </Text>
    {!isLoading && (
      <TouchableOpacity
        onPress={onRefresh}
        style={[styles.refreshButton, { backgroundColor: theme.primary }]}
        accessibilityRole="button"
        accessibilityLabel="Refresh communities"
      >
        <Text style={styles.refreshButtonText}>Refresh</Text>
      </TouchableOpacity>
    )}
  </View>
));

const styles = StyleSheet.create({
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  refreshButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  refreshButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
});
