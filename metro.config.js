const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

// Get the default config
const config = getDefaultConfig(__dirname);

// Add support for Lottie files and other assets
config.transformer = {
  ...config.transformer,
  babelTransformerPath: require.resolve('react-native-svg-transformer'),
  assetPlugins: ['expo-asset/tools/hashAssetFiles'],
  getTransformOptions: async () => ({
    transform: {
      experimentalImportSupport: false,
      inlineRequires: true,
    },
  }),
};

// Custom resolver for assets
config.resolver = {
  ...config.resolver,
  // Add support for Lottie files
  assetExts: [
    ...config.resolver.assetExts.filter(ext => ext !== 'svg'),
    'lottie',
    'json',
    'png',
    'jpg',
    'jpeg',
  ],
  sourceExts: [...config.resolver.sourceExts, 'svg'],
  // Add support for resolving modules from the assets directory
  extraNodeModules: new Proxy(
    {},
    {
      get: (target, name) => {
        if (name === 'assets') {
          return path.resolve(__dirname, 'assets');
        }
        return path.join(process.cwd(), `node_modules/${name}`);
      },
    }
  ),
};

module.exports = config;
