import React, { useCallback, useMemo, useRef, useState } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  SafeAreaView,
  Text,
  TouchableOpacity,
  ListRenderItem,
  ViewToken,
  TextInput,
  Image,
  ScrollView,
  ActivityIndicator
} from 'react-native';
import { useFocusEffect } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import WhaleLoader from '../../../components/WhaleLoader';

// Hooks
import { useCommunities } from '@src/hooks/useCommunities';
import { useTheme } from '@src/hooks/useTheme';

// Components
import { CommunityItem } from '@src/components/community/CommunityItem';

// Types
import { Community } from '@src/types/community';

// Constants
const ITEM_HEIGHT = 120;
const LOADING_ITEMS = 6;
const VIEWABILITY_CONFIG = {
  itemVisiblePercentThreshold: 50,
  minimumViewTime: 300,
};



interface ViewableItemsChanged {
  viewableItems: ViewToken[];
  changed: ViewToken[];
}

// Header component for the list
const ListHeader = React.memo(({ 
  searchQuery, 
  onSearchChange 
}: { 
  searchQuery: string; 
  onSearchChange: (text: string) => void;
}) => (
  <View style={styles.headerContainer}>
    <Text style={styles.title} accessibilityRole="header">
      Communities
    </Text>
    <View style={styles.searchContainer}>
      <Ionicons name="search" size={20} color="#666" />
      <TextInput
        style={styles.searchInput}
        placeholder="Search communities..."
        placeholderTextColor="#666"
        value={searchQuery}
        onChangeText={onSearchChange}
        returnKeyType="search"
        clearButtonMode="while-editing"
        accessibilityLabel="Search communities"
      />
    </View>
  </View>
));

ListHeader.displayName = 'ListHeader';

// Footer component for loading more indicator
const ListFooter = React.memo(({ 
  isLoadingMore 
}: { 
  isLoadingMore: boolean 
}) => {
  if (!isLoadingMore) return null;
  
  return (
    <View 
      style={styles.footerContainer}
      accessibilityElementsHidden={!isLoadingMore}
    >
      <ActivityIndicator size={24} color="#666" />
    </View>
  );
});

ListFooter.displayName = 'ListFooter';

// Empty state when no communities are found
const EmptyState = React.memo(({ 
  onRefresh, 
  isRefreshing,
  searchQuery,
  hasError = false
}: { 
  onRefresh: () => void;
  isRefreshing: boolean;
  searchQuery: string;
  hasError?: boolean;
}) => (
  <View 
    style={styles.emptyState}
    accessibilityElementsHidden={isRefreshing}
  >
    <Ionicons 
      name="people" 
      size={64} 
      color="#666" 
      accessible={false}
    />
    <Text style={styles.emptyStateText}>
      {searchQuery.trim() 
        ? 'No communities match your search.'
        : 'No communities found. Pull down to refresh.'}
    </Text>
    <TouchableOpacity 
      style={styles.refreshButton}
      onPress={onRefresh}
      disabled={isRefreshing}
      accessibilityRole="button"
      accessibilityLabel={isRefreshing ? 'Refreshing' : 'Refresh communities'}
    >
      <Text style={styles.refreshButtonText}>
        {isRefreshing ? 'Refreshing...' : 'Refresh'}
      </Text>
    </TouchableOpacity>
  </View>
));

EmptyState.displayName = 'EmptyState';

// Skeleton loader for community items
const CommunitySkeleton = React.memo(() => (
  <View 
    style={styles.skeletonContainer}
    accessibilityLabel="Loading community"
    accessibilityHint="Content is loading"
  >
    <View style={styles.skeletonImage} />
    <View style={styles.skeletonContent}>
      <View style={styles.skeletonTitle} />
      <View style={styles.skeletonDescription} />
      <View style={styles.skeletonMembers} />
    </View>
    <View style={styles.skeletonButton} />
  </View>
));

CommunitySkeleton.displayName = 'CommunitySkeleton';

// Skeleton loader list for initial loading
const SkeletonList = React.memo(() => (
  <>
    {Array.from({ length: LOADING_ITEMS }).map((_, index) => (
      <CommunitySkeleton key={`skeleton-${index}`} />
    ))}
  </>
));

const renderSkeletonItem = () => (
  <View style={styles.skeletonContainer}>
    <View style={styles.skeletonImage} />
    <View style={styles.skeletonContent}>
      <View style={styles.skeletonTitle} />
      <View style={styles.skeletonDescription} />
    </View>
  </View>
);

// Main component
export default function CommunitiesScreen() {
  const { colors } = useTheme();
  const {
    communities,
    filteredCommunities,
    searchQuery,
    isLoading,
    isRefreshing,
    isLoadingMore,
    handleSearch,
    handleRefresh,
    loadMoreCommunities,
    handleJoinToggle,
  } = useCommunities();

  // Handle search with debounce
  const handleSearchChange = useCallback((text: string) => {
    handleSearch(text);
  }, [handleSearch]);

  // Handle pull to refresh
  const onRefresh = useCallback(async () => {
    await handleRefresh();
  }, [handleRefresh]);

  // Handle infinite scroll
  const onEndReached = useCallback(() => {
    if (!isLoading && !isRefreshing && !isLoadingMore) {
      loadMoreCommunities();
    }
  }, [isLoading, isRefreshing, isLoadingMore, loadMoreCommunities]);

  // Render each community item
  const renderItem = useCallback(({ item }: { item: Community }) => (
    <CommunityItem 
      item={item} 
      onJoinToggle={handleJoinToggle} 
    />
  ), [handleJoinToggle]);

  // Memoize the list key extractor
  const extractKey = useCallback((item: Community) => item.id, []);

  // Constants
  const BASE_COMMUNITIES: Community[] = [
    {
      id: 'bitcoin-bulls',
      name: 'Bitcoin Bulls',
      description: 'Community for long-term Bitcoin investors and HODLers',
      members: 2547,
      image: 'https://assets.coingecko.com/coins/images/1/large/bitcoin.png',
      joined: true,
    },
    {
      id: 'defi-pioneers',
      name: 'DeFi Pioneers',
      description: 'Exploring the future of decentralized finance',
      members: 1832,
      image: 'https://assets.coingecko.com/coins/images/12504/large/uni.jpg',
      joined: true,
    },
    {
      id: 'nft-collectors',
      name: 'NFT Collectors',
      description: 'Digital art and NFT trading community',
      members: 3211,
      image: 'https://assets.coingecko.com/coins/images/13446/large/5f6294c0c7a8cda55cb1c936_Flow_Wordmark.png',
      joined: false,
    },
    {
      id: 'ethereum-enthusiasts',
      name: 'Ethereum Enthusiasts',
      description: 'All things Ethereum, smart contracts, and dApps',
      members: 4521,
      image: 'https://assets.coingecko.com/coins/images/279/large/ethereum.png',
      joined: true,
    },
    {
      id: 'solana-developers',
      name: 'Solana Developers',
      description: 'Technical discussions about Solana development',
      members: 1245,
      image: 'https://assets.coingecko.com/coins/images/4128/large/solana.png',
      joined: false,
    },
    {
      id: 'cardano-alliance',
      name: 'Cardano Alliance',
      description: 'Cardano ecosystem and development updates',
      members: 2876,
      image: 'https://assets.coingecko.com/coins/images/975/large/cardano.png',
      joined: true,
    },
    {
      id: 'polkadot-network',
      name: 'Polkadot Network',
      description: 'Cross-chain interoperability and parachains',
      members: 1654,
      image: 'https://assets.coingecko.com/coins/images/12171/large/polkadot.png',
      joined: false,
    },
    {
      id: 'chainlink-oracle',
      name: 'Chainlink Oracle',
      description: 'Discussion about blockchain oracles and data feeds',
      members: 987,
      image: 'https://assets.coingecko.com/coins/images/877/large/chainlink-new-logo.png',
      joined: true,
    },
    {
      id: 'avalanche-avax',
      name: 'Avalanche AVAX',
      description: 'High-performance blockchain platform community',
      members: 1432,
      image: 'https://assets.coingecko.com/coins/images/12559/large/Avalanche_Circle_RedWhite_Trans.png',
      joined: false,
    },
    {
      id: 'polygon-matic',
      name: 'Polygon Matic',
      description: 'Scaling solutions and Layer 2 discussions',
      members: 2134,
      image: 'https://assets.coingecko.com/coins/images/4713/large/matic-token-icon.png',
      joined: true,
    },
  ] as const;

  const ITEMS_PER_PAGE = 5;
  const SEARCH_DEBOUNCE_MS = 300;
  const COMMUNITY_CARD_HEIGHT = 88;

  // Loading state component using the app's LoadingState
  const FullScreenLoader = () => (
    <View style={styles.fullScreenLoaderContainer}>
      <WhaleLoader size={100} />
    </View>
  );
  
  // Track initial load state
  const isInitialLoading = isLoading && filteredCommunities.length === 0;

  // Clear search
  const handleClearSearch = useCallback((): void => {
    handleSearch('');
  }, [handleSearch]);

  // Render footer for FlatList
  const renderFooter = useCallback(() => {
    if (!isLoadingMore || isRefreshing || searchQuery.trim()) {
      return null;
    }
    
    return (
      <View style={styles.loadingMoreContainer}>
        <ActivityIndicator size={40} color="#666" />
      </View>
    );
  }, [isLoadingMore, isRefreshing, searchQuery]);

  const renderEmptyState = () => {
    const hasError = false; // TODO: Get error state from your data fetching logic
    
    return (
      <View style={styles.emptyContainer}>
        <Ionicons 
          name={hasError ? 'alert-circle-outline' : 'people-outline'} 
          size={64} 
          color="#666" 
        />
        <Text style={styles.emptyText}>
          {hasError 
            ? 'Failed to load communities. Please try again.'
            : searchQuery 
              ? 'No communities found' 
              : 'No communities available at the moment'}
        </Text>
        {hasError && (
          <TouchableOpacity 
            style={[styles.actionButton, { marginTop: 16 }]} 
            onPress={onRefresh}
            disabled={isRefreshing}
          >
            <Text style={styles.actionButtonText}>
              {isRefreshing ? 'Refreshing...' : 'Try Again'}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  // Render list header with search
  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <Text style={styles.title}>Communities</Text>
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#666" />
        <TextInput
          style={styles.searchInput}
          placeholder="Search communities..."
          placeholderTextColor="#666"
          value={searchQuery}
          onChangeText={handleSearchChange}
          returnKeyType="search"
          clearButtonMode="while-editing"
        />
      </View>
    </View>
  );

  // Render community item
  const renderCommunityItem = useCallback(({ item }: { item: Community }) => (
    <CommunityItem item={item} onJoinToggle={handleJoinToggle} />
  ), [handleJoinToggle]);

  // Key extractor
  const keyExtractor = useCallback((item: Community) => item.id, []);

  // Get item layout for optimization
  const getItemLayout = useCallback((data: Community[] | null | undefined, index: number) => ({
    length: COMMUNITY_CARD_HEIGHT,
    offset: COMMUNITY_CARD_HEIGHT * index,
    index,
  }), []);

  // Show full screen loading for initial load
  if (isInitialLoading) {
    return <FullScreenLoader />;
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header with Search */}
      <View style={styles.headerContainer}>
        <Text style={styles.title}>Communities</Text>
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={20} color="#666" />
          <TextInput
            style={styles.searchInput}
            placeholder="Search communities..."
            placeholderTextColor="#666"
            value={searchQuery}
            onChangeText={handleSearchChange}
            returnKeyType="search"
            clearButtonMode="while-editing"
          />
        </View>
      </View>
      
      {/* Main Content */}
      {isLoading && !isRefreshing ? (
        <SkeletonList />
      ) : (
        <FlatList
          data={filteredCommunities}
          renderItem={renderItem}
          keyExtractor={extractKey}
          contentContainerStyle={styles.listContainer}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={onRefresh}
              colors={[colors.primary]}
              tintColor={colors.primary}
              accessibilityLabel="Pull to refresh"
              accessibilityRole="button"
            />
          }
          onEndReached={onEndReached}
          onEndReachedThreshold={0.5}
          ListEmptyComponent={!isLoading ? (
            <View style={styles.emptyContainer}>
              <EmptyState 
                onRefresh={onRefresh} 
                isRefreshing={isRefreshing} 
                searchQuery={searchQuery} 
              />
            </View>
          ) : null}
          ListFooterComponent={
            isLoadingMore ? (
              <View style={styles.loadingMoreContainer}>
                <ActivityIndicator size={24} color="#666" />
              </View>
            ) : null
          }
          showsVerticalScrollIndicator={false}
          removeClippedSubviews
          maxToRenderPerBatch={10}
          updateCellsBatchingPeriod={50}
          initialNumToRender={10}
          windowSize={10}
        />
      )}
      
      {/* Create Community Button */}
      <TouchableOpacity 
        style={[styles.actionButton, styles.createButton]}
        onPress={() => console.log('Create community pressed')}
      >
        <Ionicons name="add" size={20} color="#fff" />
        <Text style={styles.createButtonText}>Create Community</Text>
      </TouchableOpacity>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  // Layout
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  contentContainer: {
    flex: 1,
  },
  fullScreenLoaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loaderContainer: {
    padding: 20,
    alignItems: 'center',
  },
  
  // Header
  headerContainer: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 12,
    backgroundColor: 'transparent',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#fff',
  },
  
  // List
  listContainer: {
    paddingBottom: 24,
    flexGrow: 1,
  },
  emptyListContentContainer: {
    flex: 1,
    justifyContent: 'center',
    minHeight: 300,
  },
  footerContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 24,
    lineHeight: 22,
  },
  refreshButton: {
    backgroundColor: '#3EC1F9',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    minWidth: 120,
    alignItems: 'center',
  },
  refreshButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },
  skeletonMembers: {
    width: '40%',
    height: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 4,
  },
  skeletonButton: {
    width: 64,
    height: 32,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    marginLeft: 8,
  },
  
  // Empty States
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyText: {
    color: '#666',
    fontSize: 16,
    textAlign: 'center',
    marginTop: 16,
    lineHeight: 22,
  },
  
  // Loading States
  loadingMoreContainer: {
    paddingVertical: 16,
  },
  skeletonContainer: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 12,
    alignItems: 'center',
    height: 108, // ITEM_HEIGHT - 12
  },
  skeletonImage: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  skeletonContent: {
    flex: 1,
    marginLeft: 16,
    justifyContent: 'space-between',
  },
  skeletonTitle: {
    width: '60%',
    height: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 4,
    marginBottom: 8,
  },
  skeletonDescription: {
    width: '90%',
    height: 14,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 4,
    marginBottom: 8,
  },
  
  // Community Card
  communityCard: {
    flexDirection: 'row',
    backgroundColor: '#1A1A1A',
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#333',
    minHeight: 88,
  },
  imageContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#333',
    overflow: 'hidden',
  },
  communityImage: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  communityInfo: {
    flex: 1,
    marginLeft: 16,
    paddingRight: 8,
  },
  communityName: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  communityDescription: {
    color: '#666',
    fontSize: 14,
    lineHeight: 18,
    marginBottom: 6,
  },
  membersContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  membersText: {
    color: '#666',
    fontSize: 14,
    marginLeft: 6,
    fontWeight: '400',
  },
  
  // Buttons
  actionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#3EC1F9',
    minWidth: 64,
    alignItems: 'center',
    justifyContent: 'center',
  },
  joinedButton: {
    backgroundColor: '#3EC1F9',
    borderColor: '#3EC1F9',
  },
  actionButtonText: {
    color: '#3EC1F9',
    fontWeight: '600',
    fontSize: 14,
  },
  joinedButtonText: {
    color: '#fff',
  },
  
  // Search
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1A1A1A',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#333',
    marginBottom: 16,
    marginHorizontal: 16,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    color: '#fff',
    fontSize: 16,
    fontWeight: '400',
    paddingVertical: 0,
  },
  clearButton: {
    padding: 4,
  },
  
  // Create Button
  createButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#3EC1F9',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  createButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});