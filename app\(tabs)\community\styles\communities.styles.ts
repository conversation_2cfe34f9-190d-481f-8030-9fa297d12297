import { StyleSheet } from 'react-native';
import { Theme } from '../types/theme.types';

export const getCommunitiesStyles = (theme: Theme) => 
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.background,
    },
    headerContainer: {
      padding: 16,
      backgroundColor: theme.background,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.text,
      marginBottom: 16,
    },
    footerContainer: {
      padding: 16,
      alignItems: 'center',
      justifyContent: 'center',
    },
    emptyContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      padding: 20,
    },
    emptyText: {
      fontSize: 16,
      color: theme.text,
      textAlign: 'center',
      marginTop: 16,
    },
    // Add more styles as needed
  });
