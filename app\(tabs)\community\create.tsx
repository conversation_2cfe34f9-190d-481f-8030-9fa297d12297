import React, { useState } from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity, ScrollView, Image, Switch, Platform, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import LoadingWhale from '../../../components/ui/LoadingWhale';

type Privacy = 'public' | 'private';
type Category = 'trading' | 'defi' | 'nft' | 'tech' | 'general';
type PaymentMethod = 'paypal';

const categories = [
  { id: 'trading', label: 'Trading', icon: '📈' },
  { id: 'defi', label: 'DeFi', icon: '🏦' },
  { id: 'nft', label: 'NFTs & Digital Art', icon: '🎨' },
  { id: 'tech', label: 'Technology', icon: '💻' },
  { id: 'general', label: 'General Discussion', icon: '💬' },
] as const;

interface PaymentMethodSelectorProps {
  onSelect: (method: PaymentMethod) => void;
  price: number;
}

const PaymentMethodSelector: React.FC<PaymentMethodSelectorProps> = ({ onSelect, price }) => {
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod | null>(null);

  const handlePaymentMethodSelect = (method: PaymentMethod) => {
    setSelectedMethod(method);
    onSelect(method);
  };

  return (
    <View style={styles.paymentMethodsContainer}>
      <Text style={styles.paymentMethodLabel}>Payment Method</Text>
      <TouchableOpacity
        style={[styles.paymentMethod, selectedMethod === 'paypal' && styles.selectedPaymentMethod]}
        onPress={() => handlePaymentMethodSelect('paypal')}
      >
        <Ionicons name="logo-paypal" size={24} color={selectedMethod === 'paypal' ? '#3EC1F9' : '#666'} />
        <Text style={[styles.paymentMethodText, selectedMethod === 'paypal' && styles.selectedPaymentMethodText]}>
          PayPal
        </Text>
        <Text style={styles.paymentMethodSubtext}>
          ${price.toFixed(2)}/month
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default function CreateCommunityScreen() {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [rules, setRules] = useState('');
  const [privacy, setPrivacy] = useState<Privacy>('public');
  const [selectedCategory, setSelectedCategory] = useState<Category>('general');
  const [imageUrl, setImageUrl] = useState('');
  const [isPaid, setIsPaid] = useState(false);
  const [price, setPrice] = useState('');
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleCreate = async () => {
    if (isSubmitting) return;
    
    // Validate required fields
    if (!name.trim()) {
      Alert.alert('Error', 'Please enter a community name');
      return;
    }
    
    if (!description.trim()) {
      Alert.alert('Error', 'Please enter a community description');
      return;
    }
    
    if (!rules.trim()) {
      Alert.alert('Error', 'Please enter community rules');
      return;
    }
    
    if (isPaid && (!price || parseFloat(price) <= 0)) {
      Alert.alert('Error', 'Please enter a valid price for premium community');
      return;
    }
    
    if (isPaid && !selectedPaymentMethod) {
      Alert.alert('Error', 'Please select a payment method for premium community');
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Here we would typically handle community creation
      const communityData = {
        name: name.trim(),
        description: description.trim(),
        rules: rules.trim(),
        privacy,
        category: selectedCategory,
        imageUrl: imageUrl.trim(),
        isPaid,
        price: isPaid ? parseFloat(price) : 0,
        paymentMethod: selectedPaymentMethod,
      };
      
      console.log('Creating community with data:', communityData);
      
      // Show success message
      Alert.alert(
        'Success!', 
        `Community "${name}" has been created successfully!`,
        [{ 
          text: 'OK', 
          onPress: () => router.back() 
        }]
      );
    } catch (error) {
      console.error('Error creating community:', error);
      Alert.alert(
        'Error', 
        'Failed to create community. Please try again.',
        [{ text: 'OK', style: 'default' }]
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleImageUpload = () => {
    // TODO: Implement image picker functionality
    Alert.alert(
      'Image Upload', 
      'Image upload functionality will be implemented here',
      [{ text: 'OK', style: 'default' }]
    );
  };

  const isFormValid = name.trim() && 
    description.trim() && 
    rules.trim() && 
    selectedCategory && 
    (!isPaid || (isPaid && parseFloat(price) > 0 && selectedPaymentMethod));

  const priceValue = parseFloat(price) || 0;

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.closeButton}
          onPress={() => router.back()}
          disabled={isSubmitting}
        >
          <Ionicons name="close" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.title}>Create Community</Text>
        <TouchableOpacity 
          style={[styles.createButton, (!isFormValid || isSubmitting) && styles.createButtonDisabled]}
          onPress={handleCreate}
          disabled={!isFormValid || isSubmitting}
        >
          {isSubmitting ? (
            <LoadingWhale size={20} color="#fff" />
          ) : (
            <Text style={styles.createButtonText}>Create</Text>
          )}
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Image Upload */}
        <TouchableOpacity style={styles.imageUpload} onPress={handleImageUpload}>
          {imageUrl ? (
            <Image 
              source={{ uri: imageUrl }} 
              style={styles.communityImage} 
              resizeMode="cover"
            />
          ) : (
            <>
              <View style={styles.imagePlaceholder}>
                <Ionicons name="image-outline" size={32} color="#666" />
              </View>
              <Text style={styles.imageUploadText}>Add Community Image</Text>
              <Text style={styles.imageUploadSubtext}>Tap to upload</Text>
            </>
          )}
        </TouchableOpacity>

        {/* Community Name */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Community Name *</Text>
          <TextInput
            style={styles.input}
            placeholder="Enter community name"
            placeholderTextColor="#666"
            value={name}
            onChangeText={setName}
            maxLength={50}
            editable={!isSubmitting}
          />
          <Text style={styles.charCount}>{name.length}/50</Text>
        </View>

        {/* Description */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Description *</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            placeholder="What's your community about?"
            placeholderTextColor="#666"
            value={description}
            onChangeText={setDescription}
            multiline
            numberOfLines={4}
            maxLength={500}
            textAlignVertical="top"
            editable={!isSubmitting}
          />
          <Text style={styles.charCount}>{description.length}/500</Text>
        </View>

        {/* Community Rules */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Community Rules *</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            placeholder="Set some ground rules for your community"
            placeholderTextColor="#666"
            value={rules}
            onChangeText={setRules}
            multiline
            numberOfLines={4}
            maxLength={1000}
            textAlignVertical="top"
            editable={!isSubmitting}
          />
          <Text style={styles.charCount}>{rules.length}/1000</Text>
        </View>

        {/* Category */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Category *</Text>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            style={styles.categoriesContainer}
            contentContainerStyle={styles.categoriesContent}
          >
            {categories.map((category) => (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.categoryButton,
                  selectedCategory === category.id && styles.selectedCategory
                ]}
                onPress={() => setSelectedCategory(category.id as Category)}
                disabled={isSubmitting}
              >
                <Text style={styles.categoryIcon}>{category.icon}</Text>
                <Text style={[
                  styles.categoryText,
                  selectedCategory === category.id && styles.selectedCategoryText
                ]}>
                  {category.label}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Privacy */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Privacy</Text>
          <View style={styles.privacyButtons}>
            <TouchableOpacity
              style={[
                styles.privacyButton,
                privacy === 'public' && styles.selectedPrivacy
              ]}
              onPress={() => setPrivacy('public')}
              disabled={isSubmitting}
            >
              <Ionicons 
                name="globe-outline" 
                size={24} 
                color={privacy === 'public' ? '#3EC1F9' : '#666'} 
              />
              <View style={styles.privacyTextContainer}>
                <Text style={[
                  styles.privacyTitle,
                  privacy === 'public' && styles.selectedPrivacyText
                ]}>
                  Public
                </Text>
                <Text style={styles.privacyDescription}>
                  Anyone can view and join
                </Text>
              </View>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.privacyButton,
                privacy === 'private' && styles.selectedPrivacy
              ]}
              onPress={() => setPrivacy('private')}
              disabled={isSubmitting}
            >
              <Ionicons 
                name="lock-closed-outline" 
                size={24} 
                color={privacy === 'private' ? '#3EC1F9' : '#666'} 
              />
              <View style={styles.privacyTextContainer}>
                <Text style={[
                  styles.privacyTitle,
                  privacy === 'private' && styles.selectedPrivacyText
                ]}>
                  Private
                </Text>
                <Text style={styles.privacyDescription}>
                  Must be approved to join
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>

        {/* Access Type */}
        <View style={styles.inputContainer}>
          <View style={styles.accessTypeHeader}>
            <Text style={styles.label}>Access Type</Text>
            <Switch
              value={isPaid}
              onValueChange={setIsPaid}
              trackColor={{ false: '#1A1A1A', true: 'rgba(62, 193, 249, 0.2)' }}
              thumbColor={isPaid ? '#3EC1F9' : '#666'}
              ios_backgroundColor="#1A1A1A"
              disabled={isSubmitting}
            />
          </View>
          
          <View style={[styles.accessTypeContainer, isPaid && styles.selectedAccessType]}>
            <View style={styles.accessTypeInfo}>
              <Ionicons 
                name={isPaid ? 'diamond-outline' : 'people-outline'} 
                size={24} 
                color={isPaid ? '#3EC1F9' : '#666'} 
              />
              <View style={styles.accessTypeTextContainer}>
                <Text style={[styles.accessTypeTitle, isPaid && styles.selectedAccessTypeText]}>
                  {isPaid ? 'Premium Community' : 'Free Community'}
                </Text>
                <Text style={styles.accessTypeDescription}>
                  {isPaid 
                    ? 'Members pay to join your community' 
                    : 'Anyone can join your community for free'}
                </Text>
              </View>
            </View>

            {isPaid && (
              <>
                <View style={styles.priceInputContainer}>
                  <Text style={styles.currencySymbol}>$</Text>
                  <TextInput
                    style={styles.priceInput}
                    placeholder="0.00"
                    placeholderTextColor="#666"
                    value={price}
                    onChangeText={(text) => {
                      // Only allow numbers and decimal point
                      const numericValue = text.replace(/[^0-9.]/g, '');
                      // Prevent multiple decimal points
                      const decimalCount = (numericValue.match(/\./g) || []).length;
                      if (decimalCount <= 1) {
                        setPrice(numericValue);
                      }
                    }}
                    keyboardType="decimal-pad"
                    maxLength={8}
                    editable={!isSubmitting}
                  />
                  <Text style={styles.perMonth}>/month</Text>
                </View>

                {priceValue > 0 && (
                  <PaymentMethodSelector
                    onSelect={setSelectedPaymentMethod}
                    price={priceValue}
                  />
                )}
              </>
            )}
          </View>
        </View>

        {/* Bottom spacing for scroll */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingHorizontal: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#1A1A1A',
  },
  closeButton: {
    padding: 8,
    minWidth: 40,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    flex: 1,
    textAlign: 'center',
  },
  createButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#3EC1F9',
    minWidth: 80,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 36,
  },
  createButtonDisabled: {
    opacity: 0.5,
  },
  createButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 14,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  imageUpload: {
    width: '100%',
    height: 160,
    backgroundColor: '#1A1A1A',
    borderRadius: 12,
    marginBottom: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#2A2A2A',
    borderStyle: 'dashed',
  },
  imagePlaceholder: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: '#222',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  imageUploadText: {
    color: '#666',
    fontSize: 14,
    fontWeight: '500',
  },
  imageUploadSubtext: {
    color: '#555',
    fontSize: 12,
    marginTop: 4,
  },
  communityImage: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
  },
  inputContainer: {
    marginBottom: 24,
  },
  label: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#1A1A1A',
    borderRadius: 12,
    padding: 16,
    color: '#fff',
    fontSize: 16,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  textArea: {
    height: 120,
    textAlignVertical: 'top',
  },
  charCount: {
    color: '#555',
    fontSize: 12,
    textAlign: 'right',
    marginTop: 4,
  },
  categoriesContainer: {
    marginTop: 8,
  },
  categoriesContent: {
    paddingRight: 16,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1A1A1A',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20,
    marginRight: 12,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  selectedCategory: {
    backgroundColor: 'rgba(62, 193, 249, 0.2)',
    borderColor: '#3EC1F9',
  },
  categoryIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  categoryText: {
    color: '#666',
    fontSize: 14,
    fontWeight: '500',
  },
  selectedCategoryText: {
    color: '#3EC1F9',
    fontWeight: '600',
  },
  privacyButtons: {
    gap: 12,
  },
  privacyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1A1A1A',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  selectedPrivacy: {
    borderColor: '#3EC1F9',
    backgroundColor: 'rgba(62, 193, 249, 0.1)',
  },
  privacyTextContainer: {
    marginLeft: 12,
    flex: 1,
  },
  privacyTitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  selectedPrivacyText: {
    color: '#3EC1F9',
  },
  privacyDescription: {
    color: '#666',
    fontSize: 14,
  },
  accessTypeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  accessTypeContainer: {
    backgroundColor: '#1A1A1A',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  selectedAccessType: {
    borderColor: '#3EC1F9',
    backgroundColor: 'rgba(62, 193, 249, 0.1)',
  },
  accessTypeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  accessTypeTextContainer: {
    marginLeft: 12,
    flex: 1,
  },
  accessTypeTitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  selectedAccessTypeText: {
    color: '#3EC1F9',
  },
  accessTypeDescription: {
    color: '#666',
    fontSize: 14,
  },
  priceInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
  },
  currencySymbol: {
    color: '#3EC1F9',
    fontSize: 24,
    fontWeight: 'bold',
    marginRight: 4,
  },
  priceInput: {
    flex: 1,
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
    padding: 0,
  },
  perMonth: {
    color: '#666',
    fontSize: 16,
    marginLeft: 8,
  },
  paymentMethodsContainer: {
    marginTop: 16,
    gap: 12,
  },
  paymentMethodLabel: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  paymentMethod: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#222',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  selectedPaymentMethod: {
    borderColor: '#3EC1F9',
    backgroundColor: 'rgba(62, 193, 249, 0.1)',
  },
  paymentMethodText: {
    color: '#666',
    fontSize: 16,
    marginLeft: 12,
    fontWeight: '500',
    flex: 1,
  },
  selectedPaymentMethodText: {
    color: '#3EC1F9',
    fontWeight: '600',
  },
  paymentMethodSubtext: {
    color: '#555',
    fontSize: 14,
  },
  bottomSpacing: {
    height: 40,
  },
});