import { useState, useCallback, useEffect, useMemo } from 'react';
import { Alert } from 'react-native';
import { Community, UseCommunitiesReturn } from '../types/community.types';
import { BASE_COMMUNITIES } from '../types/community.types';

const ITEMS_PER_PAGE = 10;
const SEARCH_DEBOUNCE_MS = 300;

export const useCommunities = (): UseCommunitiesReturn => {
  const [communities, setCommunities] = useState<Community[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [searchDebounceTimer, setSearchDebounceTimer] = useState<NodeJS.Timeout | null>(null);

  // Generate mock communities for pagination
  const generateMoreCommunities = useCallback((startIndex: number, count: number): Community[] => {
    const categories = ['DeFi', 'NFTs', 'Gaming', 'Layer 1', 'Layer 2', 'DAOs', 'SocialFi', 'Stablecoins'];
    
    return Array.from({ length: count }, (_, i) => {
      const isPremium = (startIndex + i) % 3 === 0;
      const isPrivate = (startIndex + i) % 5 === 0;
      const category = categories[(startIndex + i) % categories.length];
      
      return {
        id: `community-${startIndex + i}`,
        name: `Community ${startIndex + i + 1}`,
        description: `This is a ${isPremium ? 'premium ' : ''}${category.toLowerCase()} community for enthusiasts.`,
        members: Math.floor(Math.random() * 10000) + 100,
        image: `https://picsum.photos/200/200?random=${startIndex + i}`,
        joined: Math.random() > 0.5,
        isPremium,
        isPrivate,
        price: isPremium ? (i % 3 === 0 ? 9.99 : 19.99) : undefined,
        category,
        lastActive: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
      };
    });
  }, []);

  // Load initial data
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        setIsLoading(true);
        // Simulate network request
        await new Promise(resolve => setTimeout(resolve, 1000));
        setCommunities([...BASE_COMMUNITIES]);
      } catch (error) {
        console.error('Failed to load initial data:', error);
        Alert.alert('Error', 'Failed to load communities. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    loadInitialData();
  }, []);

  // Handle search with debounce
  const handleSearch = useCallback((text: string) => {
    setSearchQuery(text);
    
    if (searchDebounceTimer) {
      clearTimeout(searchDebounceTimer);
    }
    
    const timer = setTimeout(() => {
      // Search logic is handled in the filteredCommunities memo
    }, SEARCH_DEBOUNCE_MS);
    
    setSearchDebounceTimer(timer);
  }, [searchDebounceTimer]);

  // Filter communities based on search query
  const filteredCommunities = useMemo(() => {
    if (!searchQuery.trim()) return communities;
    
    const query = searchQuery.toLowerCase().trim();
    return communities.filter(community =>
      community.name.toLowerCase().includes(query) ||
      community.description.toLowerCase().includes(query) ||
      (community.category?.toLowerCase().includes(query) ?? false)
    );
  }, [communities, searchQuery]);

  // Handle refresh
  const handleRefresh = useCallback(async () => {
    try {
      setIsRefreshing(true);
      // Simulate network request
      await new Promise(resolve => setTimeout(resolve, 1000));
      setCommunities([...BASE_COMMUNITIES]);
      setSearchQuery('');
    } catch (error) {
      console.error('Failed to refresh data:', error);
      Alert.alert('Error', 'Failed to refresh communities.');
    } finally {
      setIsRefreshing(false);
    }
  }, []);

  // Load more communities for pagination
  const loadMoreCommunities = useCallback(async () => {
    if (isLoadingMore || isLoading || isRefreshing || searchQuery.trim()) {
      return;
    }
    
    try {
      setIsLoadingMore(true);
      // Simulate network request
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newCommunities = generateMoreCommunities(communities.length, ITEMS_PER_PAGE);
      setCommunities(prev => [...prev, ...newCommunities]);
    } catch (error) {
      console.error('Failed to load more communities:', error);
      Alert.alert('Error', 'Failed to load more communities.');
    } finally {
      setIsLoadingMore(false);
    }
  }, [communities.length, generateMoreCommunities, isLoading, isLoadingMore, isRefreshing, searchQuery]);

  // Toggle join/leave community
  const handleJoinToggle = useCallback((id: string, isJoining: boolean) => {
    setCommunities(prevCommunities =>
      prevCommunities.map(community =>
        community.id === id ? { ...community, joined: !community.joined } : community
      )
    );
    
    // Here you would typically make an API call to join/leave the community
    console.log(`${isJoining ? 'Leaving' : 'Joining'} community ${id}`);
  }, []);

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (searchDebounceTimer) {
        clearTimeout(searchDebounceTimer);
      }
    };
  }, [searchDebounceTimer]);

  return {
    communities,
    filteredCommunities,
    searchQuery,
    isLoading,
    isRefreshing,
    isLoadingMore,
    handleSearch,
    handleRefresh,
    loadMoreCommunities,
    handleJoinToggle,
  };
};
