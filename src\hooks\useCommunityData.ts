import { useState, useEffect, useCallback } from 'react';

// Type definitions
interface Reaction {
  emoji: string;
  count: number;
  isActive: boolean;
}

interface Comment {
  id: string;
  author: string;
  avatar: string;
  content: string;
  timestamp: string;
  likes: number;
  isLiked: boolean;
  isModerator?: boolean;
}

interface CommunityPost {
  id: string;
  author: string;
  handle: string;
  avatar: string;
  performance?: string;
  performanceType?: 'positive' | 'negative';
  date: string;
  title: string;
  content: string;
  reactions: Reaction[];
  upvotes: number;
  downvotes: number;
  comments: Comment[];
  isUpvoted: boolean;
  isDownvoted: boolean;
  showComments?: boolean;
  newComment?: string;
  isPinned?: boolean;
  memberOnly?: boolean;
  moderatorPost?: boolean;
}

interface UseCommunityDataReturn {
  posts: CommunityPost[] | null;
  isLoading: boolean;
  isRefreshing: boolean;
  isLoadingMore: boolean;
  error: string | null;
  refreshPosts: () => Promise<void>;
  loadMorePosts: () => Promise<void>;
  addPost: (post: CommunityPost) => Promise<void>;
  updatePost: (postId: string, updates: Partial<CommunityPost>) => Promise<void>;
  joinCommunity: () => Promise<void>;
  leaveCommunity: () => Promise<void>;
  refreshData: () => Promise<void>;
}

// Generate community posts
const generateCommunityPosts = (communityId: string, count: number): CommunityPost[] => {
  const posts: CommunityPost[] = [];
  const postTemplates = [
    { title: 'Weekly Market Analysis', content: 'Here\'s my take on this week\'s market movements...' },
    { title: 'New Protocol Launch', content: 'Exciting news about the latest protocol in our ecosystem...' },
    { title: 'Community AMA Announcement', content: 'Join us for an AMA session with the team...' },
    { title: 'Technical Discussion', content: 'Let\'s dive deep into the technical aspects...' },
    { title: 'Success Story', content: 'Sharing my journey and what I\'ve learned...' },
  ];

  for (let i = 0; i < count; i++) {
    const template = postTemplates[i % postTemplates.length];
    const isPinned = i === 0 && Math.random() > 0.5;
    const isModerator = Math.random() > 0.7;
    
    posts.push({
      id: `${communityId}-post-${i}`,
      author: isModerator ? 'Sarah Chen' : ['John Doe', 'Jane Smith', 'Bob Wilson'][i % 3],
      handle: isModerator ? '@sarahchen' : ['@johndoe', '@janesmith', '@bobwilson'][i % 3],
      avatar: `https://i.pravatar.cc/150?img=${i + 1}`,
      performance: Math.random() > 0.5 ? `+${(Math.random() * 20).toFixed(2)}%` : `-${(Math.random() * 10).toFixed(2)}%`,
      performanceType: Math.random() > 0.5 ? 'positive' : 'negative',
      date: new Date(Date.now() - i * 86400000).toLocaleDateString(),
      title: template.title,
      content: template.content,
      reactions: [
        { emoji: '🚀', count: Math.floor(Math.random() * 50), isActive: false },
        { emoji: '💎', count: Math.floor(Math.random() * 30), isActive: false },
        { emoji: '📈', count: Math.floor(Math.random() * 20), isActive: false },
      ],
      upvotes: Math.floor(Math.random() * 500) + 50,
      downvotes: Math.floor(Math.random() * 50),
      comments: [],
      isUpvoted: false,
      isDownvoted: false,
      isPinned,
      memberOnly: Math.random() > 0.8,
      moderatorPost: isModerator,
    });
  }

  return posts;
};

export function useCommunityData(communityId: string, isJoined: boolean): UseCommunityDataReturn {
  const [posts, setPosts] = useState<CommunityPost[] | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize posts
  useEffect(() => {
    const initializePosts = async () => {
      setIsLoading(true);
      setError(null);
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        const communityPosts = generateCommunityPosts(communityId, 10);
        setPosts(communityPosts);
      } catch (err) {
        setError('Failed to load posts');
        console.error('Error loading posts:', err);
      } finally {
        setIsLoading(false);
      }
    };

    if (communityId) {
      initializePosts();
    }
  }, [communityId]);

  const refreshPosts = useCallback(async () => {
    setIsRefreshing(true);
    setError(null);
    try {
      // Simulate API refresh
      await new Promise(resolve => setTimeout(resolve, 1500));
      const communityPosts = generateCommunityPosts(communityId, 10);
      setPosts(communityPosts);
    } catch (err) {
      setError('Failed to refresh posts');
      console.error('Error refreshing posts:', err);
    } finally {
      setIsRefreshing(false);
    }
  }, [communityId]);

  const loadMorePosts = useCallback(async () => {
    if (isLoadingMore || !posts) return;
    
    setIsLoadingMore(true);
    try {
      // Simulate loading more data
      await new Promise(resolve => setTimeout(resolve, 1000));
      const morePosts = generateCommunityPosts(communityId, 5);
      setPosts(prevPosts => [...(prevPosts || []), ...morePosts]);
    } catch (err) {
      setError('Failed to load more posts');
      console.error('Error loading more posts:', err);
    } finally {
      setIsLoadingMore(false);
    }
  }, [communityId, posts, isLoadingMore]);

  const addPost = useCallback(async (post: CommunityPost) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      setPosts(prevPosts => [post, ...(prevPosts || [])]);
    } catch (err) {
      setError('Failed to add post');
      console.error('Error adding post:', err);
      throw err;
    }
  }, []);

  const updatePost = useCallback(async (postId: string, updates: Partial<CommunityPost>) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      setPosts(prevPosts => 
        prevPosts?.map(post => 
          post.id === postId ? { ...post, ...updates } : post
        ) || null
      );
    } catch (err) {
      setError('Failed to update post');
      console.error('Error updating post:', err);
      throw err;
    }
  }, []);

  const joinCommunity = useCallback(async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      // Handle join logic
    } catch (err) {
      setError('Failed to join community');
      console.error('Error joining community:', err);
      throw err;
    }
  }, []);

  const leaveCommunity = useCallback(async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      // Handle leave logic
    } catch (err) {
      setError('Failed to leave community');
      console.error('Error leaving community:', err);
      throw err;
    }
  }, []);

  const refreshData = useCallback(async () => {
    await refreshPosts();
  }, [refreshPosts]);

  return {
    posts,
    isLoading,
    isRefreshing,
    isLoadingMore,
    error,
    refreshPosts,
    loadMorePosts,
    addPost,
    updatePost,
    joinCommunity,
    leaveCommunity,
    refreshData,
  };
}
