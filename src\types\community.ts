export interface Community {
  id: string;
  name: string;
  description: string;
  members: number;
  image: string;
  joined: boolean;
  isPremium?: boolean;
  isPrivate?: boolean;
  price?: number;
  category?: string;
  lastActive?: string;
}

export interface CommunitiesState {
  data: Community[];
  isLoading: boolean;
  isRefreshing: boolean;
  isLoadingMore: boolean;
  error: string | null;
  hasMore: boolean;
}

export interface UseCommunitiesReturnType extends CommunitiesState {
  searchQuery: string;
  filteredCommunities: Community[];
  handleSearch: (text: string) => void;
  handleRefresh: () => Promise<void>;
  handleLoadMore: () => Promise<void>;
  handleJoinToggle: (communityId: string, isJoined: boolean) => void;
  handleCreateCommunity: () => void;
  handleClearSearch: () => void;
}

export interface CommunityItemProps {
  item: Community;
  onJoinToggle: (id: string, isJoining: boolean) => void;
}

export interface LoadingStateProps {
  size?: number;
  fullScreen?: boolean;
  message?: string;
}

export interface UseCommunitiesReturn {
  communities: Community[];
  filteredCommunities: Community[];
  searchQuery: string;
  isLoading: boolean;
  isRefreshing: boolean;
  isLoadingMore: boolean;
  handleSearch: (text: string) => void;
  handleRefresh: () => Promise<void>;
  loadMoreCommunities: () => Promise<void>;
  handleJoinToggle: (id: string, isJoining: boolean) => void;
}

export const BASE_COMMUNITIES: Community[] = [
  {
    id: 'bitcoin-bulls',
    name: 'Bitcoin Bulls',
    description: 'Community for long-term Bitcoin investors and HODLers',
    members: 2547,
    image: 'https://assets.coingecko.com/coins/images/1/large/bitcoin.png',
    joined: true,
  },
  {
    id: 'defi-pioneers',
    name: 'DeFi Pioneers',
    description: 'Exploring the future of decentralized finance',
    members: 1832,
    image: 'https://assets.coingecko.com/coins/images/12504/large/uni.jpg',
    joined: true,
  },
  {
    id: 'nft-collectors',
    name: 'NFT Collectors',
    description: 'Digital art and NFT trading community',
    members: 3211,
    image: 'https://assets.coingecko.com/coins/images/13446/large/5f6294c0c7a8cda55cb1c936_Flow_Wordmark.png',
    joined: false,
  },
];
