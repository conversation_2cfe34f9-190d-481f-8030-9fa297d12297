import React, { useCallback } from 'react';
import { FlatList, StyleSheet, View, ListRenderItem, RefreshControl, Dimensions } from 'react-native';
import { Community } from '@src/types/community';
import { Theme } from '../types/theme.types';
import { CommunityItem } from '@src/components/community/CommunityItem';
import WhaleLoader from '../../../../components/WhaleLoader';
import WhaleSpinner from '../../../../components/ui/WhaleSpinner';

interface CommunityListProps {
  communities: Community[];
  isLoadingMore: boolean;
  isRefreshing: boolean;
  searchQuery: string;
  onEndReached: () => void;
  onRefresh: () => void;
  onJoinToggle: (communityId: string) => void;
  theme: Theme;
}

export const CommunityList = React.memo(({
  communities,
  isLoadingMore,
  isRefreshing,
  searchQuery,
  onEndReached,
  onRefresh,
  onJoinToggle,
  theme,
}: CommunityListProps) => {
  const renderItem: ListRenderItem<Community> = useCallback(
    ({ item }) => (
      <CommunityItem 
        item={item} 
        onJoinToggle={(communityId, isJoining) => onJoinToggle(communityId)} 
      />
    ),
    [onJoinToggle]
  );

  const keyExtractor = useCallback((item: Community) => item.id, []);

  const renderFooter = useCallback(() => {
    if (!isLoadingMore || isRefreshing || searchQuery.trim()) {
      return null;
    }
    return (
      <View style={styles.loadingMoreContainer}>
        <WhaleSpinner size={40} />
      </View>
    );
  }, [isLoadingMore, isRefreshing, searchQuery]);

  if (isRefreshing && communities.length === 0) {
    return (
      <View style={styles.loaderContainer}>
        <WhaleLoader size={100} />
      </View>
    );
  }

  return (
    <FlatList
      data={communities}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      onEndReached={onEndReached}
      onEndReachedThreshold={0.5}
      ListFooterComponent={renderFooter}
      refreshControl={
        <RefreshControl
          refreshing={false} // We handle the initial loading state with WhaleLoader
          onRefresh={onRefresh}
          tintColor={theme.primary}
          colors={[theme.primary]}
        />
      }
      contentContainerStyle={styles.contentContainer}
      initialNumToRender={10}
      maxToRenderPerBatch={10}
      windowSize={10}
      updateCellsBatchingPeriod={50}
      removeClippedSubviews={true}
    />
  );
});

const { height: WINDOW_HEIGHT } = Dimensions.get('window');

const styles = StyleSheet.create({
  contentContainer: {
    flexGrow: 1,
    padding: 8,
  },
  loadingMoreContainer: {
    padding: 16,
    alignItems: 'center',
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: WINDOW_HEIGHT * 0.6, // 60% of screen height
  },
});
