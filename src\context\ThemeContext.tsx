import React, { createContext, useState, useMemo, useContext, useEffect } from 'react';
import { useColorScheme } from 'react-native';
import { AppTheme, ThemePreference, lightTheme, darkTheme } from '../theme';

type ThemeContextType = {
  theme: AppTheme;
  themePreference: ThemePreference;
  setTheme: (themeName: ThemePreference) => void;
  toggleTheme: () => void;
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const ThemeProvider = ({ children }: { children: React.ReactNode }) => {
  const systemTheme = useColorScheme() ?? 'light';
  const [themePreference, setThemePreference] = useState<ThemePreference>('system');

  // Load saved theme preference from storage
  useEffect(() => {
    // TODO: Load from AsyncStorage
  }, []);

  // Save theme preference when it changes
  useEffect(() => {
    // TODO: Save to AsyncStorage
  }, [themePreference]);

  const theme = useMemo(() => {
    if (themePreference === 'system') {
      return systemTheme === 'dark' ? darkTheme : lightTheme;
    }
    return themePreference === 'dark' ? darkTheme : lightTheme;
  }, [systemTheme, themePreference]);

  const toggleTheme = () => {
    setThemePreference(prev => {
      if (prev === 'system') return 'dark';
      if (prev === 'dark') return 'light';
      return 'system';
    });
  };

  const value = useMemo(
    () => ({
      theme,
      themePreference,
      setTheme: setThemePreference,
      toggleTheme,
    }),
    [theme, themePreference]
  );

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useAppTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useAppTheme must be used within a ThemeProvider');
  }
  return context;
};
