// Theme types and implementations

export interface AppTheme {
  name: 'light' | 'dark';
  colors: {
    // Brand & Interaction
    primary: string;
    secondary: string;
    accent: string;

    // Backgrounds
    background: {
      primary: string;    // Main screen background
      secondary: string;  // Tab bars, headers
      tertiary: string;   // Cards, modals
    };
    
    // Text
    text: {
      primary: string;
      secondary: string;
      tertiary: string;
      disabled: string;
      inverse: string; // Text on a primary color background
    };

    // Borders & Dividers
    border: string;
    
    // Semantic Colors
    success: string;
    error: string;
    warning: string;
    
    // Performance/Wallet Indicators
    positive: string;
    negative: string;
    neutral: string;

    // Component-Specific
    skeletonBackground: string;
  };
  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
  typography: {
    h1: { fontSize: number; fontWeight: string };
    body: { fontSize: number; fontWeight: string };
  };
  radii: {
    sm: number;
    md: number;
    lg: number;
    full: number;
  };
  shadows: Record<string, any>; // Will be defined later
}

// Light Theme
export const lightTheme: AppTheme = {
  name: 'light',
  colors: {
    primary: '#3EC1F9',
    secondary: '#5A9BD5',
    accent: '#FFC000',
    background: {
      primary: '#F0F2F5',
      secondary: '#FFFFFF',
      tertiary: '#FFFFFF',
    },
    text: {
      primary: '#1C1C1E',
      secondary: '#6E6E73',
      tertiary: '#C7C7CC',
      disabled: '#AEAEB2',
      inverse: '#FFFFFF',
    },
    border: '#D1D1D6',
    success: '#34C759',
    error: '#FF3B30',
    warning: '#FF9500',
    positive: '#28A745',
    negative: '#DC3545',
    neutral: '#6E6E73',
    skeletonBackground: '#E1E1E6',
  },
  spacing: { xs: 4, sm: 8, md: 16, lg: 24, xl: 32 },
  typography: { 
    h1: { fontSize: 32, fontWeight: 'bold' }, 
    body: { fontSize: 16, fontWeight: 'normal' } 
  },
  radii: { sm: 4, md: 8, lg: 16, full: 999 },
  shadows: {},
};

// Dark Theme
export const darkTheme: AppTheme = {
  name: 'dark',
  colors: {
    primary: '#3EC1F9',
    secondary: '#5A9BD5',
    accent: '#FFC000',
    background: {
      primary: '#000000',
      secondary: '#1C1C1E',
      tertiary: '#2C2C2E',
    },
    text: {
      primary: '#FFFFFF',
      secondary: '#8E8E93',
      tertiary: '#48484A',
      disabled: '#3A3A3C',
      inverse: '#FFFFFF',
    },
    border: '#38383A',
    success: '#30D158',
    error: '#FF453A',
    warning: '#FF9F0A',
    positive: '#28A745',
    negative: '#DC3545',
    neutral: '#8E8E93',
    skeletonBackground: '#2C2C2E',
  },
  spacing: { xs: 4, sm: 8, md: 16, lg: 24, xl: 32 },
  typography: { 
    h1: { fontSize: 32, fontWeight: 'bold' }, 
    body: { fontSize: 16, fontWeight: 'normal' } 
  },
  radii: { sm: 4, md: 8, lg: 16, full: 999 },
  shadows: {},
};

// Export theme type for use in components
export type ThemeType = typeof lightTheme;

// Default export for convenience
export default {
  light: lightTheme,
  dark: darkTheme,
};
