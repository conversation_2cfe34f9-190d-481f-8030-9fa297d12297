export interface Community {
  id: string;
  name: string;
  description: string;
  members: number;
  image: string;
  joined: boolean;
  // Add any additional fields as needed
}

export interface CommunitiesState {
  data: Community[];
  isLoading: boolean;
  isRefreshing: boolean;
  isLoadingMore: boolean;
  error: string | null;
  hasMore: boolean;
}

export interface UseCommunitiesReturnType extends CommunitiesState {
  searchQuery: string;
  filteredCommunities: Community[];
  handleSearch: (text: string) => void;
  handleRefresh: () => Promise<void>;
  handleLoadMore: () => Promise<void>;
  handleJoinToggle: (communityId: string, isJoined: boolean) => void;
  handleCreateCommunity: () => void;
  handleClearSearch: () => void;
}
