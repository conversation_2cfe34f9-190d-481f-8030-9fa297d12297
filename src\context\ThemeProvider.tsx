import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useColorScheme } from 'react-native';

export type Theme = 'light' | 'dark' | 'auto';

interface ThemeContextType {
  theme: Theme;
  isDark: boolean;
  setTheme: (theme: Theme) => void;
  colors: {
    background: string;
    surface: string;
    primary: string;
    secondary: string;
    text: string;
    textSecondary: string;
    border: string;
    card: string;
    notification: string;
    error: string;
    success: string;
    warning: string;
  };
}

const lightColors = {
  background: '#ffffff',
  surface: '#f8f9fa',
  primary: '#3EC1F9',
  secondary: '#6c757d',
  text: '#212529',
  textSecondary: '#6c757d',
  border: '#dee2e6',
  card: '#ffffff',
  notification: '#dc3545',
  error: '#dc3545',
  success: '#28a745',
  warning: '#ffc107',
};

const darkColors = {
  background: '#121212',
  surface: '#1e1e1e',
  primary: '#3EC1F9',
  secondary: '#adb5bd',
  text: '#ffffff',
  textSecondary: '#adb5bd',
  border: '#343a40',
  card: '#1e1e1e',
  notification: '#dc3545',
  error: '#dc3545',
  success: '#28a745',
  warning: '#ffc107',
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const systemColorScheme = useColorScheme();
  const [theme, setTheme] = useState<Theme>('auto');

  const isDark = theme === 'auto' 
    ? systemColorScheme === 'dark' 
    : theme === 'dark';

  const colors = isDark ? darkColors : lightColors;

  const value: ThemeContextType = {
    theme,
    isDark,
    setTheme,
    colors,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

export { ThemeContext };
