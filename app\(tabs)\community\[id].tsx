import React, { useState, useCallback, useEffect, useRef } from 'react';
import { 
  SafeAreaView, 
  View, 
  Text, 
  StyleSheet, 
  Image, 
  TouchableOpacity, 
  FlatList, 
  RefreshControl,
  ScrollView,
  Share,
  Animated,
  Alert,
  Platform,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Stack, useLocalSearchParams, router } from 'expo-router';
import { useLoadingStates } from '../../../hooks/useLoadingStates';
import LoadingWhale from '../../../components/ui/LoadingWhale';

// Type definitions
interface Reaction {
  emoji: string;
  count: number;
  isActive: boolean;
}

interface Comment {
  id: string;
  author: string;
  avatar: string;
  content: string;
  timestamp: string;
  likes: number;
  isLiked: boolean;
  isModerator?: boolean;
}

interface CommunityPost {
  id: string;
  author: string;
  handle: string;
  avatar: string;
  performance?: string;
  performanceType?: 'positive' | 'negative';
  date: string;
  title: string;
  content: string;
  reactions: Reaction[];
  upvotes: number;
  downvotes: number;
  comments: Comment[];
  isUpvoted: boolean;
  isDownvoted: boolean;
  showComments?: boolean;
  newComment?: string;
  isPinned?: boolean;
  memberOnly?: boolean;
  moderatorPost?: boolean;
}

interface CommunityDetail {
  id: string;
  name: string;
  description: string;
  members: number;
  posts: number;
  image: string;
  coverImage?: string;
  joined: boolean;
  rules: string[];
  moderators: string[];
  created: string;
  category: string;
  isPrivate: boolean;
  isPremium: boolean;
  price?: number;
}

// Mock data generator
const generateCommunityDetail = (id: string): CommunityDetail => {
  const communities: Record<string, Partial<CommunityDetail>> = {
    'bitcoin-bulls': {
      name: 'Bitcoin Bulls',
      description: 'Community for long-term Bitcoin investors and HODLers. We discuss market trends, investment strategies, and the future of Bitcoin.',
      members: 2547,
      posts: 1823,
      image: 'https://assets.coingecko.com/coins/images/1/large/bitcoin.png',
      coverImage: 'https://images.unsplash.com/photo-1516245834210-c4c142787335?w=800',
      category: 'Cryptocurrency',
    },
    'defi-pioneers': {
      name: 'DeFi Pioneers',
      description: 'Exploring the future of decentralized finance. Join us to discuss yield farming, liquidity pools, and the latest DeFi protocols.',
      members: 1832,
      posts: 945,
      image: 'https://assets.coingecko.com/coins/images/12504/large/uni.jpg',
      coverImage: 'https://images.unsplash.com/photo-1639762681485-074b7f938ba0?w=800',
      category: 'DeFi',
    },
    'nft-collectors': {
      name: 'NFT Collectors',
      description: 'Digital art and NFT trading community. Share your collections, discover new artists, and stay updated on the latest drops.',
      members: 3211,
      posts: 2156,
      image: 'https://assets.coingecko.com/coins/images/13446/large/5f6294c0c7a8cda55cb1c936_Flow_Wordmark.png',
      coverImage: 'https://images.unsplash.com/photo-1646463910506-e43e17e26e58?w=800',
      category: 'NFTs',
      isPremium: true,
      price: 9.99,
    },
  };

  const baseData: CommunityDetail = {
    id,
    name: 'Community',
    description: 'A vibrant community of crypto enthusiasts.',
    members: 1000,
    posts: 500,
    image: 'https://picsum.photos/200',
    coverImage: 'https://picsum.photos/800/400',
    joined: Math.random() > 0.5,
    rules: [
      'Be respectful to all members',
      'No spam or self-promotion without permission',
      'Stay on topic - keep discussions relevant',
      'No financial advice - DYOR',
      'Report violations to moderators'
    ],
    moderators: ['Alex Thompson', 'Sarah Chen', 'Mike Rodriguez'],
    created: 'January 15, 2024',
    category: 'General',
    isPrivate: false,
    isPremium: false,
  };

  return { ...baseData, ...communities[id] };
};

// Generate community posts
const generateCommunityPosts = (communityId: string, count: number): CommunityPost[] => {
  const posts: CommunityPost[] = [];
  const postTemplates = [
    { title: 'Weekly Market Analysis', content: 'Here\'s my take on this week\'s market movements...' },
    { title: 'New Protocol Launch', content: 'Exciting news about the latest protocol in our ecosystem...' },
    { title: 'Community AMA Announcement', content: 'Join us for an AMA session with the team...' },
    { title: 'Technical Discussion', content: 'Let\'s dive deep into the technical aspects...' },
    { title: 'Success Story', content: 'Sharing my journey and what I\'ve learned...' },
  ];

  for (let i = 0; i < count; i++) {
    const template = postTemplates[i % postTemplates.length];
    const isPinned = i === 0 && Math.random() > 0.5;
    const isModerator = Math.random() > 0.7;
    
    posts.push({
      id: `${communityId}-post-${i}`,
      author: isModerator ? 'Sarah Chen' : ['John Doe', 'Jane Smith', 'Bob Wilson'][i % 3],
      handle: isModerator ? '@sarahchen' : ['@johndoe', '@janesmith', '@bobwilson'][i % 3],
      avatar: `https://i.pravatar.cc/150?img=${i + 1}`,
      performance: Math.random() > 0.5 ? `+${(Math.random() * 20).toFixed(2)}%` : `-${(Math.random() * 10).toFixed(2)}%`,
      performanceType: Math.random() > 0.5 ? 'positive' : 'negative',
      date: new Date(Date.now() - i * 86400000).toLocaleDateString(),
      title: template.title,
      content: template.content,
      reactions: [
        { emoji: '🚀', count: Math.floor(Math.random() * 50), isActive: false },
        { emoji: '💎', count: Math.floor(Math.random() * 30), isActive: false },
        { emoji: '📈', count: Math.floor(Math.random() * 20), isActive: false },
      ],
      upvotes: Math.floor(Math.random() * 500) + 50,
      downvotes: Math.floor(Math.random() * 50),
      comments: [],
      isUpvoted: false,
      isDownvoted: false,
      isPinned,
      memberOnly: Math.random() > 0.8,
      moderatorPost: isModerator,
    });
  }

  return posts;
};

// Import helper components
import { CommunityCommentsModal } from './CommunityCommentsModal';
import { CommunityPostComposer } from './CommunityPostComposer';
import { CommunityPostItem } from './CommunityPostItem';
import { useCommunityData } from '@src/hooks/useCommunityData';
import { useFocusEffect } from 'expo-router';

export default function CommunityDetailScreen() {
  const { id } = useLocalSearchParams();
  const communityId = Array.isArray(id) ? id[0] : id || '';
  
  // State management
  const [community, setCommunity] = useState<CommunityDetail | null>(null);
  const [activeTab, setActiveTab] = useState<'posts' | 'about' | 'members'>('posts');
  const [selectedPost, setSelectedPost] = useState<CommunityPost | null>(null);
  const [commentModalVisible, setCommentModalVisible] = useState(false);
  const [isComposing, setIsComposing] = useState(false);
  
  // Use community data hook - consolidated single call
  const {
    posts: communityPosts,
    isLoading: isPostsLoading,
    isRefreshing: isCommunityRefreshing,
    isLoadingMore,
    error: communityError,
    refreshPosts,
    loadMorePosts,
    addPost,
    updatePost,
    joinCommunity,
    leaveCommunity,
    refreshData
  } = useCommunityData(communityId, community?.joined || false);
  
  // Local state for posts
  const [localPosts, setLocalPosts] = useState<CommunityPost[]>([]);
  
  // Update local posts when posts from hook change
  useEffect(() => {
    if (communityPosts) {
      setLocalPosts(communityPosts);
    }
  }, [communityPosts]);
  
  // Loading states
  const {
    isInitialLoading,
    isRefreshing: isPostsRefreshing,
    setIsInitialLoading,
    setIsRefreshing: setIsPostsRefreshing
  } = useLoadingStates();

  // Load community data when component mounts or communityId changes
  useFocusEffect(
    useCallback(() => {
      const loadCommunity = async () => {
        try {
          const communityData = generateCommunityDetail(communityId);
          setCommunity(communityData);
        } catch (err) {
          console.error('Error loading community:', err);
        }
      };
      
      loadCommunity();
    }, [communityId])
  );
  
  // Handle post interactions
  const handleUpvote = useCallback((postId: string) => {
    setLocalPosts(currentPosts => 
      currentPosts.map(post => 
        post.id === postId 
          ? { 
              ...post, 
              isUpvoted: !post.isUpvoted,
              upvotes: post.isUpvoted ? post.upvotes - 1 : post.upvotes + 1,
              isDownvoted: false,
              downvotes: post.isDownvoted ? post.downvotes - 1 : post.downvotes
            } 
          : post
      )
    );
  }, []);
  
  const handleDownvote = useCallback((postId: string) => {
    setLocalPosts(currentPosts => 
      currentPosts.map(post => 
        post.id === postId 
          ? { 
              ...post, 
              isDownvoted: !post.isDownvoted,
              downvotes: post.isDownvoted ? post.downvotes - 1 : post.downvotes + 1,
              isUpvoted: false,
              upvotes: post.isUpvoted ? post.upvotes - 1 : post.upvotes
            } 
          : post
      )
    );
  }, []);
  
  // Handle post reactions with error handling and state management
  const handleReaction = useCallback((postId: string, reactionIndex: number) => {
    try {
      setLocalPosts(currentPosts => 
        currentPosts.map(p => {
          if (p.id === postId) {
            const updatedReactions = [...p.reactions];
            if (!updatedReactions[reactionIndex]) {
              console.error(`Reaction index ${reactionIndex} not found for post ${postId}`);
              return p;
            }
            
            updatedReactions[reactionIndex] = {
              ...updatedReactions[reactionIndex],
              count: updatedReactions[reactionIndex].isActive 
                ? Math.max(0, updatedReactions[reactionIndex].count - 1)
                : updatedReactions[reactionIndex].count + 1,
              isActive: !updatedReactions[reactionIndex].isActive
            };
            return { ...p, reactions: updatedReactions };
          }
          return p;
        })
      );
    } catch (error) {
      console.error('Error handling reaction:', error);
    }
  }, []);
  
  const handleAddComment = useCallback((postId: string, content: string) => {
    const newComment: Comment = {
      id: `comment-${Date.now()}`,
      author: 'You',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400',
      content,
      timestamp: 'Just now',
      likes: 0,
      isLiked: false,
      isModerator: true
    };
    
    setLocalPosts(currentPosts => 
      currentPosts.map(post => 
        post.id === postId 
          ? { 
              ...post, 
              comments: [...post.comments, newComment],
              showComments: true 
            } 
          : post
      )
    );
    
    // Call the API to add the comment
    updatePost(postId, {
      comments: [...(communityPosts?.find(p => p.id === postId)?.comments || []), newComment]
    });
  }, [communityPosts, updatePost]);
  
  const toggleComments = useCallback((postId: string) => {
    setLocalPosts(currentPosts => 
      currentPosts.map(post => 
        post.id === postId 
          ? { ...post, showComments: !post.showComments } 
          : post
      )
    );
  }, []);
  
  const handleLikeComment = useCallback((postId: string, commentId: string) => {
    setLocalPosts(currentPosts => 
      currentPosts.map(post => {
        if (post.id === postId) {
          const updatedComments = post.comments.map(comment => 
            comment.id === commentId 
              ? { 
                  ...comment, 
                  isLiked: !comment.isLiked,
                  likes: comment.isLiked ? comment.likes - 1 : comment.likes + 1
                } 
              : comment
          );
          return { ...post, comments: updatedComments };
        }
        return post;
      })
    );
  }, []);
  
  const handleReply = useCallback((postId: string, commentId: string, content: string) => {
    const newReply: Comment = {
      id: `comment-${Date.now()}`,
      author: 'You',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400',
      content,
      timestamp: 'Just now',
      likes: 0,
      isLiked: false,
      isModerator: true
    };
    
    setLocalPosts(currentPosts => 
      currentPosts.map(post => {
        if (post.id === postId) {
          const updatedComments = [...post.comments, newReply];
          return { 
            ...post, 
            comments: updatedComments,
            showComments: true 
          };
        }
        return post;
      })
    );
  }, []);
  
  const handleCreatePost = useCallback(async (content: string) => {
    try {
      const newPost: CommunityPost = {
        id: `post-${Date.now()}`,
        author: 'You',
        handle: '@you',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400',
        date: 'Just now',
        title: '',
        content,
        reactions: [
          { emoji: '👍', count: 0, isActive: false },
          { emoji: '❤️', count: 0, isActive: false },
          { emoji: '🚀', count: 0, isActive: false },
          { emoji: '🔥', count: 0, isActive: false },
          { emoji: '👏', count: 0, isActive: false },
        ],
        upvotes: 0,
        downvotes: 0,
        comments: [],
        isUpvoted: false,
        isDownvoted: false,
        isPinned: false,
        memberOnly: false,
        moderatorPost: false
      };
      
      // Add to local state immediately for better UX
      setLocalPosts(prevPosts => [newPost, ...prevPosts]);
      
      // Call the API to save the post
      await addPost(newPost);
      
      // Refresh the posts list
      await refreshData();
      
      setIsComposing(false);
    } catch (err) {
      console.error('Error creating post:', err);
      // Optionally show an error message to the user
    }
  }, [addPost, community, refreshData]);
  
  const [postText, setPostText] = useState('');
  const [isPosting, setIsPosting] = useState(false);
  const inputHeight = useRef(new Animated.Value(0)).current;

  // Load community details
  useEffect(() => {
    const loadCommunityDetails = async () => {
      try {
        setIsInitialLoading(true);
        await new Promise(resolve => setTimeout(resolve, 1000));
        const details = generateCommunityDetail(communityId);
        setCommunity(details);
      } catch (err) {
        console.error('Error loading community:', err);
      } finally {
        setIsInitialLoading(false);
      }
    };

    loadCommunityDetails();
  }, [communityId, setIsInitialLoading]);

  // Handle join/leave
  const handleJoinToggle = useCallback(async () => {
    if (!community) return;

    if (community.isPremium && !community.joined) {
      Alert.alert(
        'Premium Community',
        `Join ${community.name} for $${community.price}/month`,
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Subscribe', 
            onPress: () => {
              setCommunity(prev => prev ? { ...prev, joined: true, members: prev.members + 1 } : null);
            }
          }
        ]
      );
    } else {
      setCommunity(prev => prev ? { 
        ...prev, 
        joined: !prev.joined,
        members: prev.joined ? prev.members - 1 : prev.members + 1
      } : null);
    }
  }, [community]);

  // Handle share
  const handleShare = useCallback(async () => {
    if (!community) return;
    
    try {
      await Share.share({
        message: `Check out ${community.name} on HodlHub!\n\n${community.description}`,
        title: community.name
      });
    } catch (error) {
      console.error('Error sharing:', error);
    }
  }, [community]);

  // Post composition handlers
  const handleInputFocus = useCallback(() => {
    setIsComposing(true);
    Animated.timing(inputHeight, {
      toValue: 120,
      duration: 200,
      useNativeDriver: false,
    }).start();
  }, [inputHeight]);

  const handleInputBlur = useCallback(() => {
    if (postText.trim() === '') {
      setIsComposing(false);
      Animated.timing(inputHeight, {
        toValue: 0,
        duration: 200,
        useNativeDriver: false,
      }).start();
    }
  }, [postText, inputHeight]);

  const handlePost = useCallback(async () => {
    if (!community || postText.trim() === '' || isPosting) return;
    
    setIsPosting(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const newPost: CommunityPost = {
        id: `${communityId}-user-post-${Date.now()}`,
        author: 'You',
        handle: '@you',
        avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=400',
        date: new Date().toLocaleDateString(),
        title: 'Community Post',
        content: postText.trim(),
        reactions: [
          { emoji: '🚀', count: 0, isActive: false },
          { emoji: '💎', count: 0, isActive: false },
          { emoji: '📈', count: 0, isActive: false },
        ],
        upvotes: 0,
        downvotes: 0,
        comments: [],
        isUpvoted: false,
        isDownvoted: false,
      };
      
      addPost(newPost);
      setPostText('');
      setIsComposing(false);
      
      Animated.timing(inputHeight, {
        toValue: 0,
        duration: 200,
        useNativeDriver: false,
      }).start();
      
    } catch (err) {
      Alert.alert('Error', 'Failed to post. Please try again.');
    } finally {
      setIsPosting(false);
    }
  }, [community, postText, isPosting, communityId, addPost, inputHeight]);

  const handleCancelCompose = useCallback(() => {
    setPostText('');
    setIsComposing(false);
    Animated.timing(inputHeight, {
      toValue: 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
  }, [inputHeight]);

  // Render about tab
  const renderAboutTab = () => (
    <ScrollView style={styles.tabContent}>
      <Text style={[styles.text, styles.description]}>{community?.description}</Text>
      
      <View style={styles.section}>
        <Text style={[styles.text, styles.sectionTitle]}>Rules</Text>
        {community?.rules?.map((rule, index) => (
          <View key={index} style={styles.ruleItem}>
            <Text style={[styles.text, styles.ruleBullet]}>•</Text>
            <Text style={[styles.text, styles.ruleText]}>{rule}</Text>
          </View>
        ))}
      </View>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Moderators</Text>
        {community?.moderators?.map((moderator, index) => (
          <View key={index} style={styles.moderatorItem}>
            <Image 
              source={{ uri: 'https://i.pravatar.cc/150' }} 
              style={styles.moderatorAvatar} 
            />
            <Text style={styles.moderatorName}>{moderator}</Text>
          </View>
        ))}
      </View>
    </ScrollView>
  );

  // Render members tab
  const renderMembersTab = () => (
    <View style={[styles.tabContent, { padding: 16 }]}>
      <Text style={[styles.text, { fontSize: 18, fontWeight: '600', marginBottom: 8 }]}>
        {community?.members?.toLocaleString()} members
      </Text>
      <Text style={[styles.text, { color: '#888', marginBottom: 16 }]}>
        Active members in this community
      </Text>
      
      <FlatList
        data={[1, 2, 3, 4, 5]} // Mock data
        keyExtractor={(item) => item.toString()}
        renderItem={({ item }) => (
          <View style={styles.memberItem}>
            <Image 
              source={{ uri: `https://i.pravatar.cc/150?img=${item}` }} 
              style={{
                width: 50,
                height: 50,
                borderRadius: 25,
                marginRight: 12,
              }} 
            />
            <View style={{ flex: 1, justifyContent: 'center' }}>
              <Text style={[styles.text, { fontWeight: '600' }]}>Member {item}</Text>
              <Text style={[styles.text, { color: '#888', fontSize: 14 }]}>@member{item}</Text>
            </View>
            <TouchableOpacity 
              style={{
                paddingHorizontal: 16,
                paddingVertical: 8,
                backgroundColor: '#1a1a1a',
                borderRadius: 20,
                justifyContent: 'center',
              }}
            >
              <Text style={[styles.text, { color: '#3EC1F9' }]}>Follow</Text>
            </TouchableOpacity>
          </View>
        )}
      />
    </View>
  );

  // Loading state
  if (isInitialLoading || !community) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3EC1F9" />
          <Text style={styles.loadingText}>Loading community...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{community?.name}</Text>
        <TouchableOpacity style={styles.shareButton} onPress={handleShare}>
          <Ionicons name="share-social" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      <Image 
        source={{ uri: community?.coverImage || 'https://picsum.photos/800/400' }} 
        style={styles.coverImage} 
      />

      <View style={styles.communityInfo}>
        <View style={styles.communityHeader}>
          <Image 
            source={{ uri: community?.image || 'https://picsum.photos/200' }} 
            style={styles.communityImage} 
          />
          <View style={styles.communityDetails}>
            <View style={styles.nameRow}>
              <Text style={styles.communityName}>{community?.name}</Text>
              {community?.isPrivate && (
                <Ionicons name="lock-closed" size={20} color="#fff" style={styles.lockIcon} />
              )}
            </View>
            <View style={styles.statsRow}>
              <Text style={styles.statText}>{community?.members?.toLocaleString()} members</Text>
              <Text style={styles.statDivider}>•</Text>
              <Text style={styles.statText}>{community?.posts} posts</Text>
            </View>
          </View>
        </View>
        <TouchableOpacity 
          style={[styles.joinButton, community?.joined && styles.joinedButton]}
          onPress={handleJoinToggle}
        >
          <Text style={[styles.joinButtonText, community?.joined && styles.joinedButtonText]}>
            {community?.joined ? 'Leave' : community?.isPremium ? `Join • $${community?.price}/mo` : 'Join'}
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.tabs}>
        <TouchableOpacity 
          style={[styles.tab, activeTab === 'posts' && styles.activeTab]}
          onPress={() => setActiveTab('posts')}
        >
          <Text style={[styles.tabText, activeTab === 'posts' && styles.activeTabText]}>Posts</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.tab, activeTab === 'about' && styles.activeTab]}
          onPress={() => setActiveTab('about')}
        >
          <Text style={[styles.tabText, activeTab === 'about' && styles.activeTabText]}>About</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.tab, activeTab === 'members' && styles.activeTab]}
          onPress={() => setActiveTab('members')}
        >
          <Text style={[styles.tabText, activeTab === 'members' && styles.activeTabText]}>Members</Text>
        </TouchableOpacity>
      </View>

      {activeTab === 'posts' ? (
        <>
          {/* Post Composer */}
          {community?.joined && (
            <View style={styles.composeContainer}>
              <TextInput
                style={[styles.input, { minHeight: 100 }]}
                placeholder="What's on your mind?"
                placeholderTextColor="#666"
                value={postText}
                onChangeText={setPostText}
                onFocus={handleInputFocus}
                onBlur={handleInputBlur}
                multiline
              />
              <View style={styles.composeButtons}>
                <TouchableOpacity 
                  style={styles.cancelButton}
                  onPress={handleCancelCompose}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity 
                  style={[styles.postButton, (!postText.trim() || isPosting) && styles.postButtonDisabled]}
                  onPress={handlePost}
                  disabled={!postText.trim() || isPosting}
                >
                  {isPosting ? (
                    <ActivityIndicator size="small" color="#fff" />
                  ) : (
                    <Text style={styles.postButtonText}>Post</Text>
                  )}
                </TouchableOpacity>
              </View>
            </View>
          )}

          {/* Posts List */}
          <FlatList
            data={localPosts}
            renderItem={({ item }) => (
              <View style={styles.postContainer}>
                <View style={styles.postHeader}>
                  <Image source={{ uri: item.avatar }} style={styles.avatar} />
                  <View style={styles.postAuthor}>
                    <Text style={styles.postAuthorName}>{item.author}</Text>
                    <Text style={styles.postHandle}>{item.handle}</Text>
                  </View>
                </View>
                <Text style={styles.postContent}>{item.content}</Text>
                <View style={styles.postActions}>
                  <TouchableOpacity 
                    style={styles.actionButton}
                    onPress={() => handleUpvote(item.id)}
                  >
                    <Ionicons 
                      name={item.isUpvoted ? 'arrow-up-circle' : 'arrow-up-circle-outline'} 
                      size={24} 
                      color={item.isUpvoted ? '#3EC1F9' : '#fff'} 
                    />
                    <Text style={styles.actionCount}>{item.upvotes}</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity 
                    style={styles.actionButton}
                    onPress={() => handleDownvote(item.id)}
                  >
                    <Ionicons 
                      name={item.isDownvoted ? 'arrow-down-circle' : 'arrow-down-circle-outline'} 
                      size={24} 
                      color={item.isDownvoted ? '#FF3B30' : '#fff'} 
                    />
                    <Text style={styles.actionCount}>{item.downvotes}</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity 
                    style={styles.actionButton}
                    onPress={() => {
                      setSelectedPost(item);
                      setCommentModalVisible(true);
                    }}
                  >
                    <Ionicons name="chatbubble-outline" size={20} color="#fff" />
                    <Text style={styles.actionCount}>{item.comments?.length || 0}</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity 
                    style={styles.actionButton}
                    onPress={handleShare}
                  >
                    <Ionicons name="share-social-outline" size={20} color="#fff" />
                  </TouchableOpacity>
                </View>
                
                {/* Reactions */}
                <View style={styles.reactionsContainer}>
                  {item.reactions?.map((reaction, index) => (
                    <TouchableOpacity 
                      key={index}
                      style={[styles.reaction, reaction.isActive && styles.activeReaction]}
                      onPress={() => handleReaction(item.id, index)}
                    >
                      <Text style={styles.reactionEmoji}>{reaction.emoji}</Text>
                      <Text style={styles.reactionCount}>{reaction.count}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            )}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.postsContainer}
            refreshControl={
              <RefreshControl
                refreshing={isPostsRefreshing}
                onRefresh={refreshPosts}
                tintColor="#3EC1F9"
                colors={['#3EC1F9']}
              />
            }
            onEndReached={loadMorePosts}
            onEndReachedThreshold={0.5}
            ListFooterComponent={
              isLoadingMore ? (
                <View style={styles.loadingMore}>
                  <LoadingWhale size={40} color="#3EC1F9" />
                </View>
              ) : null
            }
            ListEmptyComponent={
              !isPostsLoading ? (
                <View style={styles.emptyState}>
                  <Ionicons name="document-text-outline" size={64} color="#666" />
                  <Text style={styles.emptyStateText}>
                    {community?.joined ? 'No posts yet. Be the first to post!' : 'Join the community to see posts'}
                  </Text>
                </View>
              ) : null
            }
          />
        </>
      ) : activeTab === 'about' ? (
        renderAboutTab()
      ) : (
        renderMembersTab()
      )}

      {/* Comments Modal */}
      <CommunityCommentsModal
        visible={commentModalVisible}
        post={selectedPost}
        onClose={() => setCommentModalVisible(false)}
        onAddComment={handleAddComment}
        onLikeComment={handleLikeComment}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  text: {
    color: '#fff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#fff',
    marginTop: 10,
    fontSize: 16,
  },
  // Post styles
  postContainer: {
    backgroundColor: '#1a1a1a',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  postHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  postAuthor: {
    flex: 1,
  },
  postAuthorName: {
    color: '#fff',
    fontWeight: '600',
  },
  postHandle: {
    color: '#888',
    fontSize: 12,
  },
  postContent: {
    color: '#fff',
    fontSize: 16,
    lineHeight: 22,
    marginBottom: 12,
  },
  postActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#2a2a2a',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 4,
  },
  actionCount: {
    color: '#888',
    marginLeft: 4,
    fontSize: 14,
  },
  reactionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  reaction: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2a2a2a',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginRight: 8,
    marginBottom: 8,
  },
  activeReaction: {
    backgroundColor: 'rgba(62, 193, 249, 0.2)',
  },
  reactionEmoji: {
    marginRight: 4,
  },
  reactionCount: {
    color: '#fff',
    fontSize: 12,
  },
  // Compose styles
  composeContainer: {
    backgroundColor: '#1a1a1a',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  input: {
    color: '#fff',
    fontSize: 16,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  composeButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 12,
  },
  cancelButton: {
    padding: 8,
    marginRight: 12,
  },
  cancelButtonText: {
    color: '#888',
    fontSize: 16,
  },
  postButton: {
    backgroundColor: '#3EC1F9',
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  postButtonDisabled: {
    opacity: 0.5,
  },
  postButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#000',
    borderBottomWidth: 1,
    borderBottomColor: '#222',
  },
  backButton: {
    padding: 4,
  },
  shareButton: {
    padding: 4,
  },
  headerTitle: {
    flex: 1,
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginHorizontal: 16,
  },
  coverImage: {
    width: '100%',
    height: 150,
    backgroundColor: '#1a1a1a',
  },
  communityInfo: {
    backgroundColor: '#000',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#222',
  },
  communityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  communityImage: {
    width: 64,
    height: 64,
    borderRadius: 32,
    marginRight: 12,
  },
  communityDetails: {
    flex: 1,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  communityName: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  lockIcon: {
    marginLeft: 6,
  },
  statsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  statText: {
    color: '#666',
    fontSize: 14,
  },
  statDivider: {
    color: '#666',
    marginHorizontal: 8,
  },
  joinButton: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#3EC1F9',
    marginLeft: 12,
  },
  joinedButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#3EC1F9',
  },
  joinButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 14,
  },
  joinedButtonText: {
    color: '#3EC1F9',
  },
  tabs: {
    flexDirection: 'row',
    backgroundColor: '#000',
    borderBottomWidth: 1,
    borderBottomColor: '#222',
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#3EC1F9',
  },
  tabText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '600',
  },
  activeTabText: {
    color: '#fff',
  },
  postsContainer: {
    flexGrow: 1,
  },
  loadingMore: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 80,
  },
  emptyStateText: {
    color: '#666',
    fontSize: 16,
    marginTop: 16,
    textAlign: 'center',
  },
  aboutContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  section: {
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#222',
  },
  sectionTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  sectionContent: {
    color: '#ccc',
    fontSize: 16,
    lineHeight: 22,
  },
  ruleItem: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  ruleNumber: {
    color: '#3EC1F9',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  ruleText: {
    color: '#ccc',
    fontSize: 16,
    flex: 1,
    lineHeight: 22,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoText: {
    color: '#ccc',
    fontSize: 16,
    marginLeft: 12,
  },
  moderatorItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  moderatorAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  moderatorName: {
    color: '#fff',
    fontSize: 16,
    flex: 1,
  },
  moderatorBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(62, 193, 249, 0.2)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  moderatorBadgeText: {
    color: '#3EC1F9',
    fontSize: 12,
    fontWeight: '600',
  },
  membersContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  membersTitle: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  membersSubtitle: {
    color: '#666',
    fontSize: 16,
    textAlign: 'center',
  },
});