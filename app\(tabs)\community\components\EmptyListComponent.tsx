import React, { memo } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface EmptyListComponentProps {
  searchQuery: string;
  isLoading: boolean;
}

export const EmptyListComponent = memo(({ searchQuery, isLoading }: EmptyListComponentProps) => {
  if (isLoading) {
    return null;
  }

  return (
    <View style={styles.emptyListContainer}>
      <Ionicons name="people-outline" size={64} color="#666" />
      <Text style={styles.emptyListText}>
        {searchQuery.trim() ? 'No communities found' : 'No communities available'}
      </Text>
      {searchQuery.trim() && (
        <Text style={styles.emptyListSubtext}>
          Try adjusting your search terms
        </Text>
      )}
    </View>
  );
});

EmptyListComponent.displayName = 'EmptyListComponent';

const styles = StyleSheet.create({
  emptyListContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 60,
    minHeight: 200,
  },
  emptyListText: {
    color: '#666',
    fontSize: 18,
    fontWeight: '500',
    textAlign: 'center',
    marginTop: 16,
  },
  emptyListSubtext: {
    color: '#666',
    fontSize: 14,
    textAlign: 'center',
    marginTop: 8,
    opacity: 0.7,
  },
});
